# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

server:
  port: 1157
spring:
  application:
    name: ${HOSTNAME:@hertzbeat@}${PID}
  profiles:
    active: prod
  mvc:
    static-path-pattern: /**
  jackson:
    default-property-inclusion: ALWAYS
  web:
    resources:
      static-locations:
        - classpath:/dist/
        - classpath:../dist/
  # need to disable spring boot mongodb auto config, or default mongodb connection tried and failed..
  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration, org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration
  freemarker:
    enabled: false
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB

management:
  health:
    mail:
      enabled: off
  endpoints:
    web:
      exposure:
        include:
          - 'metrics'
          - 'health'
    enabled-by-default: on

sureness:
  container: jakarta_servlet
  auths:
    - digest
    - basic
    - jwt
  jwt:
    secret: 'CyaFv0bwq2Eik0jdrKUtsA6bx3sDJeFV643R
             LnfKefTjsIfJLBa2YkhEqEGtcHDTNe4CU6+9
             8tVt4bisXQ13rbN0oxhUZR73M6EByXIO+SV5
             dKhaX0csgOCTlCxq20yhmUea6H6JIpSE2Rwp'


---
spring:
  config:
    activate:
      on-profile: prod
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456
    url: *******************************************************************************************************************
    platform: mysql
    hikari:
      max-lifetime: 120000
  jpa:
    show-sql: false
    database-platform: org.eclipse.persistence.platform.database.MySQLPlatform
    database: mysql
    properties:
      eclipselink:
        logging:
          level: SEVERE
  
  flyway:
    enabled: true
    clean-disabled: true
    baseline-on-migrate: true
    baseline-version: 1
    locations:
      - classpath:db/migration/mysql
  
  # Not Require, Please config if you need email notify
  # 非必填：不使用邮箱作为警告通知可以去掉spring.mail配置
  mail:
    # Attention: this is mail server address.
    # 请注意此为邮件服务器地址：qq邮箱为 smtp.qq.com qq企业邮箱为 smtp.exmail.qq.com
    host: smtp.qq.com
    username: <EMAIL>
    # Attention: this is not email account password, this requires an email authorization code
    # 请注意此非邮箱账户密码 此需填写邮箱授权码
    password: xxqzvuqbnqvbbdac
    port: 465
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          socketFactoryClass: javax.net.ssl.SSLSocketFactory
          ssl:
            enable: true

warehouse:
  store:
    # store history metrics data, enable only one below
    # 存储历史数据方式, 下方只能enabled启用一种方式
    jpa:
      enabled: false
      expire-time: 1h
    victoria-metrics:
      enabled: true
      url: http://victoria-metrics:8428      
      username: root
      password: root
  # store real-time metrics data, enable only one below
  real-time:
    memory:
      enabled: true
      init-size: 16
    redis:
      enabled: false
      # redis mode: single, sentinel, cluster. Default is single
      mode: single
      # separate each address with comma when using cluster mode, eg: 127.0.0.1:6379,127.0.0.1:6380
      address: 127.0.0.1:6379
      # enter master name when using sentinel mode
      masterName: mymaster
      password: 123456
      # redis db index, default: DB0
      db: 0

common:
  queue:
    # memory or kafka
    type: memory

alerter:
  # custom console url
  console-url: https://console.tancloud.io
  # we work
  we-work-webhook-url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=
  # ding ding talk
  ding-talk-webhook-url: https://oapi.dingtalk.com/robot/send?access_token=
  # fei shu fly book
  fly-book-webhook-url: https://open.feishu.cn/open-apis/bot/v2/hook/
  # telegram
  telegram-webhook-url: https://api.telegram.org/bot%s/sendMessage
  # discord
  discord-webhook-url: https://discord.com/api/v9/channels/%s/messages
  # serverChan
  server-chan-webhook-url: https://sctapi.ftqq.com/%s.send
  # gotify
  gotify-webhook-url: http://127.0.0.1/message?token=%s
  # alert inhibit ttl unit ms, default 14400000(4 hours)
  inhibit:
    ttl: 14400000
  sms:
    enable: false
    type: tencent
    tencent:
      secret-id:
      secret-key:
      app-id:
      sign-name:
      template-id:
    alibaba:
      access-key-id:
      access-key-secret:
      sign-name:
      template-code:
    unisms:
      # auth-mode: simple or hmac
      auth-mode: simple
      access-key-id: YOUR_ACCESS_KEY_ID
      # hmac mode need to fill in access-key-secret
      access-key-secret: YOUR_ACCESS_KEY_SECRET
      signature: YOUR_SMS_SIGNATURE
      template-id: YOUR_TEMPLATE_ID
    smslocal:
      api-key: YOUR_API_KEY_HERE
    aws:
      access-key-id: YOUR_ACCESS_KEY_ID
      access-key-secret: YOUR_ACCESS_KEY_SECRET
      region: AWS_REGION_FOR_END_USER_MESSAGING
    twilio:
      account-sid: YOUR_ACCOUNT_SID
      auth-token: YOUR_AUTH_TOKEN 
      twilio-phone-number: YOUR_TWILIO_PHONE_NUMBER
scheduler:
  server:
    enabled: true
    port: 1158

grafana:
  enabled: false
  url: http://127.0.0.1:3000
  username: admin
  password: admin
