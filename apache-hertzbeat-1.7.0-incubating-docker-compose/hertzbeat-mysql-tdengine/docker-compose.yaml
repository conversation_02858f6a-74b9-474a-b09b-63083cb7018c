# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

version: "3.7"

networks:
  hertzbeat:
    driver: bridge

services:
  mysql:
    image: mariadb:11.7
    container_name: compose-mysql
    hostname: mysql
    restart: always
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--connect", "--innodb_initialized"]
      interval: 10s
      retries: 5
      start_period: 30s
    ports:
      - '13306:3306'
    environment:
      TZ: Asia/Shanghai
      MARIADB_ROOT_PASSWORD: 123456
    volumes:
      - ./dbdata/mysqldata:/var/lib/mysql/
      - ./conf/sql:/docker-entrypoint-initdb.d/
    networks:
      - hertzbeat

  tdengine:
    image: tdengine/tdengine:*******
    container_name: compose-tdengine
    hostname: tdengine
    restart: always
    healthcheck:
      test: ['CMD-SHELL', 'curl -u root:taosdata -d "show databases" tdengine:6041/rest/sql']
      interval: 10s
      retries: 5
      timeout: 5s
      start_period: 1m
    environment:
      TZ: Asia/Shanghai
    ports:
      - "16041:6041"
    volumes:
      - ./dbdata/taosdata:/var/lib/taos/
    networks:
      - hertzbeat

  hertzbeat:
    image: apache/hertzbeat:1.7.0
    container_name: compose-hertzbeat
    hostname: hertzbeat
    restart: always
    environment:
      TZ: Asia/Shanghai
      LANG: zh_CN.UTF-8
    depends_on:
      mysql:
        condition: service_healthy
      tdengine:
        condition: service_healthy
    volumes:
      - ./conf/application.yml:/opt/hertzbeat/config/application.yml
      - ./conf/sureness.yml:/opt/hertzbeat/config/sureness.yml
      - ./ext-lib:/opt/hertzbeat/ext-lib
      - ./logs:/opt/hertzbeat/logs
    ports:
      - "1157:1157"
      - "1158:1158"
    networks:
      - hertzbeat
