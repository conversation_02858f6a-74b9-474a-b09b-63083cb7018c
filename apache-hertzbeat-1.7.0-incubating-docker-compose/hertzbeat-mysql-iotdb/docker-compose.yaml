# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

version: "3.7"

networks:
  hertzbeat:
    driver: bridge

services:
  mysql:
    image: mariadb:11.7
    container_name: compose-mysql
    hostname: mysql
    restart: always
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--connect", "--innodb_initialized"]
      interval: 10s
      retries: 5
      start_period: 30s
    ports:
      - '13306:3306'
    environment:
      TZ: Asia/Shanghai
      MARIADB_ROOT_PASSWORD: 123456
    volumes:
      - ./dbdata/mysqldata:/var/lib/mysql/
      - ./conf/sql:/docker-entrypoint-initdb.d/
    networks:
      - hertzbeat

  iotdb:
    image: apache/iotdb:1.2.2-standalone
    container_name: compose-iotdb
    hostname: iotdb
    restart: always
    healthcheck:
      test: ["CMD", "ls", "/iotdb/data"]
      interval: 10s
      retries: 5
      timeout: 5s
      start_period: 30s
    environment:
      TZ: Asia/Shanghai
    ports:
      - "18181:8181"
      - "16667:6667"
    volumes:
      - ./dbdata/iotdbdata:/iotdb/data
    networks:
      - hertzbeat

  hertzbeat:
    image: apache/hertzbeat:1.7.0
    container_name: compose-hertzbeat
    hostname: hertzbeat
    restart: always
    environment:
      TZ: Asia/Shanghai
      LANG: zh_CN.UTF-8
    depends_on:
      mysql:
        condition: service_healthy
      iotdb:
        condition: service_healthy
    volumes:
      - ./conf/application.yml:/opt/hertzbeat/config/application.yml
      - ./conf/sureness.yml:/opt/hertzbeat/config/sureness.yml
      - ./ext-lib:/opt/hertzbeat/ext-lib
      - ./logs:/opt/hertzbeat/logs
    ports:
      - "1157:1157"
      - "1158:1158"
    networks:
      - hertzbeat
