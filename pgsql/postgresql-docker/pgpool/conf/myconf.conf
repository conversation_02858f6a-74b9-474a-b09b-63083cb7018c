num_init_children = 200         # 每个Pgpool最多接收200并发连接
max_pool = 4                    # 每个user/database/backend连接池最大连接数

listen_addresses = '*'
port = 9999                #  修改为你想使用的端口
pcp_port = 9898            #  管理接口端口（可选）

sr_check_user = 'repmgr'
sr_check_password = '123456'

health_check_user = 'repmgr'
health_check_password = '123456'
health_check_period = 10
health_check_database = 'repmgr'

####################################
# BACKEND NODES
####################################
backend_hostname0 = 'pg-0'
backend_port0 = 5432
backend_weight0 = 1
# 标准主从集群	所有节点设为 ALLOW_TO_FAILOVER
# 某节点做归档、不参与切换	设置为 DISALLOW_TO_FAILOVER
# 单主配置，业务不允许自动切主	设置所有从节点为 DISALLOW_TO_FAILOVER，仅主为 ALLOW_TO_FAILOVER
backend_flag0 = 'ALLOW_TO_FAILOVER'

backend_hostname1 = 'pg-1'
backend_port1 = 5432
backend_weight1 = 1
backend_flag1 = 'ALLOW_TO_FAILOVER'

####################################
# LOAD BALANCING
####################################
load_balance_mode = on
ignore_leading_white_space = on
white_function_list = ''
black_function_list = 'currval,lastval,nextval,setval'

####################################
# HEALTH CHECK
####################################
health_check_period = 5
health_check_timeout = 3
health_check_user = 'postgres'
health_check_password = '123456'
health_check_database = 'postgres'
health_check_max_retries = 3
health_check_retry_delay = 1

####################################
# WATCHDOG（若不配置可跳过）
####################################
use_watchdog = on
wd_hostname = 'pgpool-0'            # 当前节点主机名
wd_port = 9000                    # watchdog通信端口
wd_authkey = '123456'      # watchog认证密钥，所有节点保持一致
delegate_IP = '**********'        # 浮动虚拟IP，客户端访问此IP
# 所有watchdog节点信息，至少包含本节点和其他节点
hostname0 = '**********'
pgpool_port0 = 5432
wd_port0 = 9000

hostname1 = '**********'
pgpool_port1 = 5432
wd_port1 = 9000
# heartbeat心跳配置，节点间互相检测
heartbeat_destination0 = '**********'
heartbeat_destination_port0 = 9694
heartbeat_device0 = 'eth0'

heartbeat_destination1 = '**********'
heartbeat_destination_port1 = 9694
heartbeat_device1 = 'eth0'

####################################
# OTHERS
####################################
# 是否启用类似 PostgreSQL 的 pg_hba.conf 认证方式。
enable_pool_hba = on
allow_clear_text_frontend_auth = on
log_destination = 'stderr'
print_timestamp = on
log_connections = on
log_hostname = on
# 是否记录每个 SQL 语句。off  none（别名） all：记录所有语句 errors：只记录执行失败的语句
log_statement = errors