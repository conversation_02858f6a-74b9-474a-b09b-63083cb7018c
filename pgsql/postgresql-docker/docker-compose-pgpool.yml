services:
  pgpool-0:
    image: harbor.cqt.com:11000/docker/bitnami/pgpool:latest
    container_name: pgpool-0
    hostname: pgpool-0
    network_mode: host
    volumes:
      - ./pgpool/conf/myconf.conf:/config/myconf.conf
    environment:
      - PGPOOL_USER_CONF_FILE=/config/myconf.conf
      # 后端PostgreSQL节点配置
      - PGPOOL_BACKEND_NODES=0:pg-0:5432,1:pg-1:5432

      # 数据库连接配置
      - PGPOOL_POSTGRES_USERNAME=postgres
      - PGPOOL_POSTGRES_PASSWORD=123456

      - PGPOOL_POSTGRES_CUSTOM_USERS=custom
      - PGPOOL_POSTGRES_CUSTOM_PASSWORDS=123456

      # 流复制检查用户
      - PGPOOL_SR_CHECK_USER=repmgr
      - PGPOOL_SR_CHECK_PASSWORD=123456
      - PGPOOL_SR_CHECK_PERIOD=10

      # 健康检查配置
      - PGPOOL_HEALTH_CHECK_USER=postgres
      - PGPOOL_HEALTH_CHECK_PASSWORD=123456
      - PGPOOL_HEALTH_CHECK_PERIOD=30
      - PGPOOL_HEALTH_CHECK_TIMEOUT=10
      - PGPOOL_HEALTH_CHECK_MAX_RETRIES=3

      # 管理员配置
      - PGPOOL_ADMIN_USERNAME=admin
      - PGPOOL_ADMIN_PASSWORD=admin123

      # 连接池配置
      - PGPOOL_NUM_INIT_CHILDREN=200
      - PGPOOL_MAX_POOL=4

      # 负载均衡配置
      - PGPOOL_ENABLE_LOAD_BALANCING=yes
      - PGPOOL_DISABLE_LOAD_BALANCE_ON_WRITE=transaction

      # 故障转移配置
      - PGPOOL_AUTO_FAILBACK=no
      - PGPOOL_FAILOVER_COMMAND='/opt/bitnami/scripts/pgpool/failover.sh %d %h %p %D %m %H %M %P %r %R'

      # 其他配置
      - PGPOOL_ENABLE_LDAP=no
      - PGPOOL_ENABLE_TLS=no

    restart: always
    extra_hosts:
      - "pg-0:**********"
      - "pg-1:**********"
      - "pgpool-0:**********"
      - "pgpool-1:**********"
    healthcheck:
      test: ["CMD", "/opt/bitnami/scripts/pgpool/healthcheck.sh"]
      interval: 10s
      timeout: 5s
      retries: 5

  pgpool-1:
    image: harbor.cqt.com:11000/docker/bitnami/pgpool:latest
    container_name: pgpool-1
    hostname: pgpool-1
    network_mode: host
    volumes:
      - ./pgpool/conf/myconf.conf:/config/myconf.conf
    environment:
      - PGPOOL_USER_CONF_FILE=/config/myconf.conf
      # 后端PostgreSQL节点配置
      - PGPOOL_BACKEND_NODES=0:pg-0:5432,1:pg-1:5432

      # 数据库连接配置
      - PGPOOL_POSTGRES_USERNAME=postgres
      - PGPOOL_POSTGRES_PASSWORD=123456

      - PGPOOL_POSTGRES_CUSTOM_USERS=custom
      - PGPOOL_POSTGRES_CUSTOM_PASSWORDS=123456

      # 流复制检查用户
      - PGPOOL_SR_CHECK_USER=repmgr
      - PGPOOL_SR_CHECK_PASSWORD=123456
      - PGPOOL_SR_CHECK_PERIOD=10

      # 健康检查配置
      - PGPOOL_HEALTH_CHECK_USER=postgres
      - PGPOOL_HEALTH_CHECK_PASSWORD=123456
      - PGPOOL_HEALTH_CHECK_PERIOD=30
      - PGPOOL_HEALTH_CHECK_TIMEOUT=10
      - PGPOOL_HEALTH_CHECK_MAX_RETRIES=3

      # 管理员配置
      - PGPOOL_ADMIN_USERNAME=admin
      - PGPOOL_ADMIN_PASSWORD=admin123

      # 连接池配置
      - PGPOOL_NUM_INIT_CHILDREN=200
      - PGPOOL_MAX_POOL=4

      # 负载均衡配置
      - PGPOOL_ENABLE_LOAD_BALANCING=yes
      - PGPOOL_DISABLE_LOAD_BALANCE_ON_WRITE=transaction

      # 故障转移配置
      - PGPOOL_AUTO_FAILBACK=no
      - PGPOOL_FAILOVER_COMMAND='/opt/bitnami/scripts/pgpool/failover.sh %d %h %p %D %m %H %M %P %r %R'

      # 其他配置
      - PGPOOL_ENABLE_LDAP=no
      - PGPOOL_ENABLE_TLS=no

    restart: always
    extra_hosts:
      - "pg-0:**********"
      - "pg-1:**********"
      - "pgpool-0:**********"
      - "pgpool-1:**********"
    healthcheck:
      test: ["CMD", "/opt/bitnami/scripts/pgpool/healthcheck.sh"]
      interval: 10s
      timeout: 5s
      retries: 5
