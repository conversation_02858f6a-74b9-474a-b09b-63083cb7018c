
```shell
pg-0 10.255.1.2
pg-1 10.255.1.3
pgpool-0 10.255.1.2
pgpool-1 10.255.1.3

```

```shell

mkdir -p /home/<USER>/postgresql/logs/
mkdir -p /home/<USER>/postgresql/conf/
mkdir -p /home/<USER>/pgpool/conf/
mkdir -p /home/<USER>/postgresql/pgdata/

chmod -R g+rwX /home/<USER>/postgresql/logs/
chgrp -R root /home/<USER>/postgresql/logs/
chmod -R g+rwX /home/<USER>/postgresql/conf/
chgrp -R root /home/<USER>/postgresql/conf/
chmod -R g+rwX /home/<USER>/postgresql/pgdata/
chgrp -R root /home/<USER>/postgresql/pgdata/

chmod -R g+rwX /home/<USER>/pgpool/conf/
chgrp -R root /home/<USER>/pgpool/conf/

```


```shell

docker exec -it -u root pgpool /bin/bash

docker-compose -f /home/<USER>/docker-compose-pgpool.yml up -d

docker-compose -f /home/<USER>/docker-compose-postgresql-repmgr.yml up pg-0 -d
docker-compose -f /home/<USER>/docker-compose-postgresql-repmgr.yml up pg-1 -d
```